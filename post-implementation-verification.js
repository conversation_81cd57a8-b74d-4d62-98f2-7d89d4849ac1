const { chromium } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

// Mobile breakpoints to test
const breakpoints = [
  { name: 'iPhone SE', width: 320, height: 568 },
  { name: 'iPhone 12/13 mini', width: 375, height: 812 },
  { name: 'iPhone 12/13/14', width: 414, height: 896 },
  { name: 'iPad Portrait', width: 768, height: 1024 },
];

// Pages to audit
const pages = [
  { name: 'Homepage', url: '/' },
  { name: 'About', url: '/about' },
  { name: 'Contact', url: '/contact' },
  { name: 'Growth Audit', url: '/growth-audit' },
  { name: 'Core Implementation', url: '/core-implementation' },
  { name: 'Wellness Clinics', url: '/wellness-clinics' },
  { name: 'Medical Spas', url: '/medical-spas' },
  { name: 'Holistic Functional Medicine', url: '/holistic-functional-medicine' },
  { name: 'Multi-practitioner Centers', url: '/multi-practitioner-centers' },
];

const baseURL = 'http://localhost:3000';

async function verifyMobileFixes() {
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  const results = {
    timestamp: new Date().toISOString(),
    summary: {
      totalPages: pages.length,
      totalBreakpoints: breakpoints.length,
      fixesVerified: {
        marketingSherpaCard: { pass: 0, fail: 0, details: [] },
        buttonTextWrapping: { pass: 0, fail: 0, details: [] },
        wordHyphenation: { pass: 0, fail: 0, details: [] },
        footerElements: { pass: 0, fail: 0, details: [] }
      },
      overallStatus: 'pending'
    },
    detailedResults: []
  };

  // Create verification screenshots directory
  const screenshotsDir = path.join(__dirname, 'post-implementation-screenshots');
  if (!fs.existsSync(screenshotsDir)) {
    fs.mkdirSync(screenshotsDir, { recursive: true });
  }

  console.log('Starting post-implementation verification audit...\n');

  for (const pageInfo of pages) {
    console.log(`Verifying fixes on page: ${pageInfo.name} (${pageInfo.url})`);
    
    const pageResults = {
      pageName: pageInfo.name,
      url: pageInfo.url,
      breakpointResults: []
    };

    for (const breakpoint of breakpoints) {
      console.log(`  Testing breakpoint: ${breakpoint.name} (${breakpoint.width}px)`);
      
      // Set viewport
      await page.setViewportSize({ 
        width: breakpoint.width, 
        height: breakpoint.height 
      });

      try {
        // Navigate to page
        await page.goto(`${baseURL}${pageInfo.url}`, { 
          waitUntil: 'networkidle', 
          timeout: 30000 
        });

        // Wait for any animations or dynamic content
        await page.waitForTimeout(2000);

        const breakpointResult = {
          breakpointName: breakpoint.name,
          width: breakpoint.width,
          height: breakpoint.height,
          fixVerifications: {
            marketingSherpaCard: { status: 'not_applicable', details: '' },
            buttonTextWrapping: { status: 'pass', details: [] },
            wordHyphenation: { status: 'pass', details: [] },
            footerElements: { status: 'pass', details: [] }
          },
          screenshot: null
        };

        // Take screenshot
        const screenshotPath = path.join(screenshotsDir, `${pageInfo.name.replace(/[^a-zA-Z0-9]/g, '-')}-${breakpoint.width}px-post-fix.png`);
        await page.screenshot({ 
          path: screenshotPath, 
          fullPage: true 
        });
        breakpointResult.screenshot = screenshotPath;

        // 1. VERIFY MARKETING SHERPA CARD FIX
        const marketingSherpaCardCheck = await page.evaluate(() => {
          // Look for Marketing Sherpa logo and card elements
          const logoElements = Array.from(document.querySelectorAll('img')).filter(img => 
            img.src && img.src.includes('marketing-sherpa') || 
            img.alt && img.alt.toLowerCase().includes('marketing sherpa')
          );
          
          if (logoElements.length === 0) {
            return { found: false, details: 'Marketing Sherpa card not found on this page' };
          }

          const results = [];
          logoElements.forEach((logo, index) => {
            const card = logo.closest('[class*="card"], .bg-white, .rounded, .border, .shadow') || 
                         logo.closest('div[class*="p-"], div[class*="bg-"]');
            
            if (card) {
              const logoRect = logo.getBoundingClientRect();
              const cardRect = card.getBoundingClientRect();
              
              // Check if logo is positioned on top (logo's top should be near card's top)
              const isLogoOnTop = (logoRect.top - cardRect.top) < (cardRect.height * 0.3);
              
              // Find text content in the card
              const textElements = Array.from(card.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div')).filter(el => {
                const text = el.textContent.trim();
                return text.length > 10 && !el.querySelector('img');
              });
              
              let textBelowLogo = true;
              if (textElements.length > 0) {
                textBelowLogo = textElements.some(textEl => {
                  const textRect = textEl.getBoundingClientRect();
                  return textRect.top > logoRect.bottom - 10; // Text should be below logo
                });
              }
              
              results.push({
                logoIndex: index,
                logoPosition: { top: logoRect.top, bottom: logoRect.bottom },
                cardPosition: { top: cardRect.top, bottom: cardRect.bottom },
                isLogoOnTop: isLogoOnTop,
                textBelowLogo: textBelowLogo,
                textElementsFound: textElements.length,
                status: isLogoOnTop && textBelowLogo ? 'pass' : 'fail'
              });
            }
          });
          
          return { found: true, results: results };
        });

        if (marketingSherpaCardCheck.found) {
          const passCount = marketingSherpaCardCheck.results.filter(r => r.status === 'pass').length;
          const totalCount = marketingSherpaCardCheck.results.length;
          
          breakpointResult.fixVerifications.marketingSherpaCard = {
            status: passCount === totalCount ? 'pass' : 'fail',
            details: marketingSherpaCardCheck.results
          };
          
          if (passCount === totalCount) {
            results.summary.fixesVerified.marketingSherpaCard.pass++;
          } else {
            results.summary.fixesVerified.marketingSherpaCard.fail++;
          }
          results.summary.fixesVerified.marketingSherpaCard.details.push({
            page: pageInfo.name,
            breakpoint: breakpoint.name,
            status: passCount === totalCount ? 'pass' : 'fail',
            details: marketingSherpaCardCheck.results
          });
        }

        // 2. VERIFY BUTTON TEXT WRAPPING FIX
        const buttonWrappingCheck = await page.evaluate(() => {
          const buttons = Array.from(document.querySelectorAll('button, a[class*="button"], .btn, [role="button"]'));
          const issues = [];
          
          buttons.forEach((btn, index) => {
            const rect = btn.getBoundingClientRect();
            const isVisible = rect.width > 0 && rect.height > 0;
            
            if (isVisible) {
              const textContent = btn.textContent.trim();
              if (textContent.length > 0) {
                // Check if button text is overflowing
                const scrollWidth = btn.scrollWidth;
                const clientWidth = btn.clientWidth;
                const isOverflowing = scrollWidth > clientWidth + 2; // Small tolerance
                
                if (isOverflowing) {
                  issues.push({
                    index: index,
                    text: textContent.substring(0, 50),
                    scrollWidth: scrollWidth,
                    clientWidth: clientWidth,
                    overflow: scrollWidth - clientWidth
                  });
                }
              }
            }
          });
          
          return { totalButtons: buttons.length, overflowingButtons: issues };
        });

        if (buttonWrappingCheck.overflowingButtons.length === 0) {
          results.summary.fixesVerified.buttonTextWrapping.pass++;
          breakpointResult.fixVerifications.buttonTextWrapping = {
            status: 'pass',
            details: [`All ${buttonWrappingCheck.totalButtons} buttons wrap text properly`]
          };
        } else {
          results.summary.fixesVerified.buttonTextWrapping.fail++;
          breakpointResult.fixVerifications.buttonTextWrapping = {
            status: 'fail',
            details: buttonWrappingCheck.overflowingButtons.map(btn => 
              `Button "${btn.text}" overflows by ${btn.overflow}px`
            )
          };
        }

        // 3. VERIFY WORD HYPHENATION FIX
        const hyphenationCheck = await page.evaluate(() => {
          const textElements = Array.from(document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div'));
          const hyphenatedElements = [];
          
          textElements.forEach((el, index) => {
            const text = el.textContent;
            if (text) {
              // Look for words ending with hyphens at line breaks
              const hyphenPattern = /\w+-\s*$/gm;
              const matches = text.match(hyphenPattern);
              
              if (matches && matches.length > 0) {
                const style = window.getComputedStyle(el);
                const hyphens = style.hyphens || style.webkitHyphens || 'none';
                
                hyphenatedElements.push({
                  index: index,
                  tagName: el.tagName,
                  className: el.className,
                  hyphenMatches: matches,
                  cssHyphens: hyphens,
                  textSample: text.substring(0, 100)
                });
              }
            }
          });
          
          return { totalElements: textElements.length, hyphenatedElements: hyphenatedElements };
        });

        if (hyphenationCheck.hyphenatedElements.length === 0) {
          results.summary.fixesVerified.wordHyphenation.pass++;
          breakpointResult.fixVerifications.wordHyphenation = {
            status: 'pass',
            details: [`No unwanted hyphenation found in ${hyphenationCheck.totalElements} text elements`]
          };
        } else {
          results.summary.fixesVerified.wordHyphenation.fail++;
          breakpointResult.fixVerifications.wordHyphenation = {
            status: 'fail',
            details: hyphenationCheck.hyphenatedElements.map(el => 
              `${el.tagName} element has hyphenated words: ${el.hyphenMatches.join(', ')}`
            )
          };
        }

        // 4. VERIFY FOOTER ELEMENTS FIX
        const footerCheck = await page.evaluate(() => {
          const footers = Array.from(document.querySelectorAll('footer, [class*="footer"]'));
          const issues = [];
          let totalLogos = 0;
          let totalEmails = 0;
          
          if (footers.length === 0) {
            return { found: false, details: 'No footer found on this page' };
          }
          
          footers.forEach((footer, footerIndex) => {
            // Check for logos in footer
            const logoImages = Array.from(footer.querySelectorAll('img')).filter(img => 
              img.src && (img.src.includes('logo') || img.alt && img.alt.toLowerCase().includes('logo'))
            );
            totalLogos += logoImages.length;
            
            // Check for email addresses
            const emailElements = Array.from(footer.querySelectorAll('*')).filter(el => {
              const text = el.textContent;
              return text && /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/.test(text);
            });
            totalEmails += emailElements.length;
            
            // Check if elements overflow
            logoImages.forEach((logo, logoIndex) => {
              const rect = logo.getBoundingClientRect();
              const viewportWidth = window.innerWidth;
              
              if (rect.right > viewportWidth || rect.width > viewportWidth) {
                issues.push({
                  type: 'logo-overflow',
                  element: `Logo ${logoIndex + 1} in footer ${footerIndex + 1}`,
                  width: rect.width,
                  right: rect.right,
                  viewportWidth: viewportWidth
                });
              }
            });
            
            emailElements.forEach((email, emailIndex) => {
              const rect = email.getBoundingClientRect();
              const viewportWidth = window.innerWidth;
              
              if (rect.right > viewportWidth || email.scrollWidth > email.clientWidth) {
                issues.push({
                  type: 'email-overflow',
                  element: `Email ${emailIndex + 1} in footer ${footerIndex + 1}`,
                  text: email.textContent.trim(),
                  width: rect.width,
                  scrollWidth: email.scrollWidth,
                  clientWidth: email.clientWidth
                });
              }
            });
          });
          
          return { 
            found: true, 
            footerCount: footers.length,
            logoCount: totalLogos,
            emailCount: totalEmails,
            issues: issues 
          };
        });

        if (footerCheck.found) {
          if (footerCheck.issues.length === 0) {
            results.summary.fixesVerified.footerElements.pass++;
            breakpointResult.fixVerifications.footerElements = {
              status: 'pass',
              details: [`Footer elements properly sized - ${footerCheck.logoCount} logos, ${footerCheck.emailCount} emails`]
            };
          } else {
            results.summary.fixesVerified.footerElements.fail++;
            breakpointResult.fixVerifications.footerElements = {
              status: 'fail',
              details: footerCheck.issues.map(issue => 
                `${issue.type}: ${issue.element} - ${issue.text ? issue.text.substring(0, 50) : 'overflow detected'}`
              )
            };
          }
        } else {
          breakpointResult.fixVerifications.footerElements = {
            status: 'not_applicable',
            details: 'No footer found on this page'
          };
        }

        pageResults.breakpointResults.push(breakpointResult);
        
        const passCount = Object.values(breakpointResult.fixVerifications).filter(v => v.status === 'pass').length;
        console.log(`    ${passCount}/4 fixes verified successfully`);

      } catch (error) {
        console.error(`    Error testing ${pageInfo.name} at ${breakpoint.name}:`, error.message);
        
        const errorResult = {
          breakpointName: breakpoint.name,
          width: breakpoint.width,
          height: breakpoint.height,
          fixVerifications: {
            marketingSherpaCard: { status: 'error', details: error.message },
            buttonTextWrapping: { status: 'error', details: error.message },
            wordHyphenation: { status: 'error', details: error.message },
            footerElements: { status: 'error', details: error.message }
          },
          screenshot: null
        };
        
        pageResults.breakpointResults.push(errorResult);
      }
    }
    
    results.detailedResults.push(pageResults);
    console.log(`Completed ${pageInfo.name}\n`);
  }

  await browser.close();
  
  // Calculate overall status
  const totalTests = results.summary.fixesVerified;
  const totalPasses = Object.values(totalTests).reduce((sum, fix) => sum + fix.pass, 0);
  const totalFails = Object.values(totalTests).reduce((sum, fix) => sum + fix.fail, 0);
  
  if (totalFails === 0) {
    results.summary.overallStatus = 'all_fixes_verified';
  } else if (totalPasses > totalFails) {
    results.summary.overallStatus = 'mostly_fixed';
  } else {
    results.summary.overallStatus = 'issues_remain';
  }
  
  // Generate report
  const reportPath = path.join(__dirname, 'post-implementation-verification-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
  
  return results;
}

// Generate human-readable HTML report
function generateVerificationHtmlReport(results) {
  const reportPath = path.join(__dirname, 'post-implementation-verification-report.html');
  
  const statusColors = {
    pass: '#10b981',
    fail: '#ef4444',
    not_applicable: '#6b7280',
    error: '#dc2626'
  };

  let html = `
<!DOCTYPE html>
<html>
<head>
    <title>Post-Implementation Verification Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .summary { background: #f3f4f6; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        .fix-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
        .fix-card { background: white; padding: 15px; border-radius: 8px; border-left: 4px solid; }
        .fix-card.pass { border-color: ${statusColors.pass}; }
        .fix-card.fail { border-color: ${statusColors.fail}; }
        .page-section { margin-bottom: 40px; border-bottom: 1px solid #e5e7eb; padding-bottom: 20px; }
        .breakpoint { margin-left: 20px; margin-bottom: 20px; background: #f9fafb; padding: 15px; border-radius: 8px; }
        .fix-result { margin: 10px 0; padding: 10px; border-radius: 4px; border-left: 3px solid; }
        .fix-result.pass { border-color: ${statusColors.pass}; background: #ecfdf5; }
        .fix-result.fail { border-color: ${statusColors.fail}; background: #fef2f2; }
        .fix-result.not_applicable { border-color: ${statusColors.not_applicable}; background: #f9fafb; }
        .fix-result.error { border-color: ${statusColors.error}; background: #fef2f2; }
        .screenshot { max-width: 300px; border: 1px solid #d1d5db; margin: 10px 0; }
        h1, h2, h3 { color: #111827; }
        .status-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; color: white; }
        .status-badge.pass { background: ${statusColors.pass}; }
        .status-badge.fail { background: ${statusColors.fail}; }
        .status-badge.not_applicable { background: ${statusColors.not_applicable}; }
        .overall-status { font-size: 18px; font-weight: bold; padding: 10px; border-radius: 8px; text-align: center; }
        .overall-status.all_fixes_verified { background: #ecfdf5; color: #065f46; }
        .overall-status.mostly_fixed { background: #fff7ed; color: #9a3412; }
        .overall-status.issues_remain { background: #fef2f2; color: #991b1b; }
    </style>
</head>
<body>
    <h1>Post-Implementation Verification Report</h1>
    <p><em>Generated: ${new Date(results.timestamp).toLocaleString()}</em></p>
    
    <div class="summary">
        <h2>Overall Status</h2>
        <div class="overall-status ${results.summary.overallStatus}">
            ${results.summary.overallStatus.replace(/_/g, ' ').toUpperCase()}
        </div>
        
        <h3>Fix Verification Summary</h3>
        <div class="fix-summary">
            <div class="fix-card ${results.summary.fixesVerified.marketingSherpaCard.fail === 0 ? 'pass' : 'fail'}">
                <h4>Marketing Sherpa Card</h4>
                <p>✓ Pass: ${results.summary.fixesVerified.marketingSherpaCard.pass}</p>
                <p>✗ Fail: ${results.summary.fixesVerified.marketingSherpaCard.fail}</p>
            </div>
            
            <div class="fix-card ${results.summary.fixesVerified.buttonTextWrapping.fail === 0 ? 'pass' : 'fail'}">
                <h4>Button Text Wrapping</h4>
                <p>✓ Pass: ${results.summary.fixesVerified.buttonTextWrapping.pass}</p>
                <p>✗ Fail: ${results.summary.fixesVerified.buttonTextWrapping.fail}</p>
            </div>
            
            <div class="fix-card ${results.summary.fixesVerified.wordHyphenation.fail === 0 ? 'pass' : 'fail'}">
                <h4>Word Hyphenation</h4>
                <p>✓ Pass: ${results.summary.fixesVerified.wordHyphenation.pass}</p>
                <p>✗ Fail: ${results.summary.fixesVerified.wordHyphenation.fail}</p>
            </div>
            
            <div class="fix-card ${results.summary.fixesVerified.footerElements.fail === 0 ? 'pass' : 'fail'}">
                <h4>Footer Elements</h4>
                <p>✓ Pass: ${results.summary.fixesVerified.footerElements.pass}</p>
                <p>✗ Fail: ${results.summary.fixesVerified.footerElements.fail}</p>
            </div>
        </div>
        
        <p><strong>Total Pages Tested:</strong> ${results.summary.totalPages}</p>
        <p><strong>Total Breakpoints:</strong> ${results.summary.totalBreakpoints}</p>
    </div>
`;

  results.detailedResults.forEach(pageResult => {
    html += `
    <div class="page-section">
        <h2>${pageResult.pageName} (${pageResult.url})</h2>
`;

    pageResult.breakpointResults.forEach(breakpointResult => {
      html += `
        <div class="breakpoint">
            <h3>${breakpointResult.breakpointName} (${breakpointResult.width}px)</h3>
`;

      if (breakpointResult.screenshot) {
        html += `<img src="${path.basename(breakpointResult.screenshot)}" alt="Screenshot" class="screenshot">`;
      }

      // Show fix verification results
      Object.entries(breakpointResult.fixVerifications).forEach(([fixName, result]) => {
        const displayName = fixName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        html += `
          <div class="fix-result ${result.status}">
            <strong>${displayName}:</strong> 
            <span class="status-badge ${result.status}">${result.status.toUpperCase()}</span>
`;
        
        if (Array.isArray(result.details)) {
          result.details.forEach(detail => {
            if (typeof detail === 'string') {
              html += `<br>• ${detail}`;
            } else {
              html += `<br>• ${JSON.stringify(detail)}`;
            }
          });
        } else if (typeof result.details === 'string') {
          html += `<br>${result.details}`;
        }
        
        html += `</div>`;
      });

      html += `</div>`;
    });

    html += `</div>`;
  });

  html += `
</body>
</html>`;

  fs.writeFileSync(reportPath, html);
  console.log(`HTML verification report generated: ${reportPath}`);
}

// Run the verification
(async () => {
  try {
    console.log('Post-Implementation Mobile Responsiveness Verification Starting...\n');
    const results = await verifyMobileFixes();
    
    console.log('\n=== VERIFICATION COMPLETE ===');
    console.log(`Overall Status: ${results.summary.overallStatus.replace(/_/g, ' ').toUpperCase()}`);
    
    console.log('\nFix Verification Results:');
    Object.entries(results.summary.fixesVerified).forEach(([fixName, counts]) => {
      const displayName = fixName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      console.log(`  ${displayName}: ${counts.pass} pass, ${counts.fail} fail`);
    });
    
    generateVerificationHtmlReport(results);
    
    console.log('\nVerification report files generated:');
    console.log('- post-implementation-verification-report.json (detailed JSON data)');
    console.log('- post-implementation-verification-report.html (human-readable report)');
    console.log('- post-implementation-screenshots/ (verification screenshots)');
    
  } catch (error) {
    console.error('Verification failed:', error);
    process.exit(1);
  }
})();