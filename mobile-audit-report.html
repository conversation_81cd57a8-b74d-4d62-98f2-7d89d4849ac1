
<!DOCTYPE html>
<html>
<head>
    <title>Mobile Responsiveness Audit Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .summary { background: #f3f4f6; padding: 20px; border-radius: 8px; margin-bottom: 30px; }
        .page-section { margin-bottom: 40px; border-bottom: 1px solid #e5e7eb; padding-bottom: 20px; }
        .breakpoint { margin-left: 20px; margin-bottom: 20px; }
        .issue { margin: 10px 0; padding: 10px; border-left: 4px solid; border-radius: 4px; background: #f9fafb; }
        .issue.critical { border-color: #dc2626; background: #fef2f2; }
        .issue.high { border-color: #ea580c; background: #fff7ed; }
        .issue.medium { border-color: #ca8a04; background: #fefce8; }
        .issue.low { border-color: #65a30d; background: #f0fdf4; }
        .screenshot { max-width: 200px; border: 1px solid #d1d5db; margin: 10px 0; }
        h1, h2, h3 { color: #111827; }
        .no-issues { color: #059669; font-weight: bold; }
    </style>
</head>
<body>
    <h1>Mobile Responsiveness Audit Report</h1>
    
    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Pages Tested:</strong> 9</p>
        <p><strong>Total Breakpoints:</strong> 4</p>
        <p><strong>Total Tests:</strong> 36</p>
        <p><strong>Total Issues Found:</strong> 879</p>
        
        <h3>Issues by Severity</h3>
        <ul>
            <li><span style="color: #dc2626">Critical:</span> 0</li>
            <li><span style="color: #ea580c">High:</span> 533</li>
            <li><span style="color: #ca8a04">Medium:</span> 346</li>
            <li><span style="color: #65a30d">Low:</span> 0</li>
        </ul>
    </div>

    <div class="page-section">
        <h2>Homepage (/)</h2>

        <div class="breakpoint">
            <h3>iPhone SE (320px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Homepage-320px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 320x9989, client: 320x568)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.mobile-spacing.max-w-7xl (scroll: 360x1322, client: 320x1298)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-2.gap-12.items-center.min-h-[600px] (scroll: 344x1322, client: 288x1298)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.relative (scroll: 344x344, client: 320x320)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.mobile-spacing.max-w-7xl (scroll: 336x1556, client: 320x1556)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.content-spacing (scroll: 320x1556, client: 288x1556)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-2.gap-8.max-w-6xl.mx-auto (scroll: 320x1300, client: 288x1300)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.mobile-spacing.max-w-7xl (scroll: 320x1000, client: 320x940)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-2.gap-12.items-center (scroll: 288x1000, client: 288x940)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50.w-fit (12px) - "Strategic Consultancy for Wellness Leaders"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A (252x20px) - "Learn More About Growth Audits"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A (252x20px) - "Explore Implementation Project"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (220x28px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (220x28px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 264px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: Professional-wellness-marketing-consultation.jpg (natural: 2048px, display: 320px)</div><div class="issue high"><strong>IMAGE-OVERFLOW:</strong> Image overflows viewport: Professional-wellness-marketing-consultation.jpg (width: 320px, viewport: 320px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 320px)</div><div class="issue high"><strong>IMAGE-OVERFLOW:</strong> Image overflows viewport: wellness-marketing-maestros-logo.png (width: 320px, viewport: 320px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13 mini (375px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Homepage-375px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 375x9008, client: 375x812)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 375x67, client: 375x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 343x67, client: 343x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.mobile-spacing.max-w-7xl (scroll: 383x1274, client: 375x1250)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-2.gap-12.items-center.min-h-[600px] (scroll: 367x1274, client: 343x1250)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.relative (scroll: 367x367, client: 343x343)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.content-spacing (scroll: 351x1317, client: 343x1317)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-2.gap-8.max-w-6xl.mx-auto (scroll: 351x1061, client: 343x1061)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.mobile-spacing.max-w-7xl (scroll: 375x833, client: 375x773)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50.w-fit (12px) - "Strategic Consultancy for Wellness Leaders"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A (283x20px) - "Learn More About Growth Audits"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A (283x20px) - "Explore Implementation Project"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (275x28px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (275x28px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-charcoal-500.hover:underline (227x20px) - "featured in Marketing Sherpa"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 319px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: Professional-wellness-marketing-consultation.jpg (natural: 2048px, display: 343px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 343px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13/14 (414px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Homepage-414px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 414x8584, client: 414x896)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 414x72, client: 414x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 382x72, client: 382x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.mobile-spacing.max-w-7xl (scroll: 422x1281, client: 414x1257)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-2.gap-12.items-center.min-h-[600px] (scroll: 406x1281, client: 382x1257)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.relative (scroll: 406x406, client: 382x382)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.mobile-spacing.max-w-7xl (scroll: 414x813, client: 414x753)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-2.gap-12.items-center (scroll: 382x813, client: 382x753)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50.w-fit (12px) - "Strategic Consultancy for Wellness Leaders"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A (314x20px) - "Learn More About Growth Audits"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A (314x20px) - "Explore Implementation Project"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (314x28px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (314x28px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (314x28px) - "Holistic & Functional Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 358px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: Professional-wellness-marketing-consultation.jpg (natural: 2048px, display: 382px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 382px)</div></div>
        <div class="breakpoint">
            <h3>iPad Portrait (768px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Homepage-768px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 768x8202, client: 768x1024)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 768x66, client: 768x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 736x66, client: 736x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.mobile-spacing.max-w-7xl (scroll: 768x1440, client: 768x1416)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-2.gap-12.items-center.min-h-[600px] (scroll: 744x1440, client: 720x1416)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: H1.text-4xl.md:text-5xl.lg:text-6xl.font-bold.text-charcoal.leading-tight (scroll: 720x101, client: 720x96)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.relative (scroll: 744x744, client: 720x720)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.mobile-spacing.max-w-7xl (scroll: 768x635, client: 768x575)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-2.gap-12.items-center (scroll: 720x635, client: 720x575)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50.w-fit (12px) - "Strategic Consultancy for Wellness Leaders"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-charcoal-700.hover:text-strategy-blue.transition-colors.font-medium (34x24px) - "Blog"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (588x32px) - "Growth Audit & Strategic Roadm"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A (652x20px) - "Learn More About Growth Audits"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (588x32px) - "Core Implementation Projects"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A (652x20px) - "Explore Implementation Project"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (652x28px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (652x28px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (652x28px) - "Holistic & Functional Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-charcoal-500.hover:underline (227x20px) - "featured in Marketing Sherpa"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 267px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: Professional-wellness-marketing-consultation.jpg (natural: 2048px, display: 720px)</div></div></div>
    <div class="page-section">
        <h2>About (/about)</h2>

        <div class="breakpoint">
            <h3>iPhone SE (320px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\About-320px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 320x9233, client: 320x568)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.max-w-4xl.mx-auto.content-spacing (scroll: 296x1707, client: 288x1707)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.md:grid-cols-2.gap-8 (scroll: 296x1432, client: 288x1432)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.mobile-spacing.max-w-7xl (scroll: 336x557, client: 320x557)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-2.gap-12.items-center (scroll: 320x557, client: 288x557)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl.py-16 (scroll: 338x1120, client: 320x1120)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-5.gap-8 (scroll: 322x835, client: 288x835)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.lg:col-span-2 (scroll: 322x325, client: 320x325)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.space-y-2.text-cloud-grey-100 (scroll: 322x76, client: 320x76)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50.w-fit.mx-auto (12px) - "Our Mission & Philosophy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 264px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: Mahmoudou%20Sidibe%20Profile%20Photo.jpg (natural: 1000px, display: 288px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: Professional-wellness-marketing-consultation.jpg (natural: 2048px, display: 320px)</div><div class="issue high"><strong>IMAGE-OVERFLOW:</strong> Image overflows viewport: Professional-wellness-marketing-consultation.jpg (width: 320px, viewport: 320px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 320px)</div><div class="issue high"><strong>IMAGE-OVERFLOW:</strong> Image overflows viewport: wellness-marketing-maestros-logo.png (width: 320px, viewport: 320px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13 mini (375px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\About-375px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 375x8189, client: 375x812)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 375x67, client: 375x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 343x67, client: 343x64)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50.w-fit.mx-auto (12px) - "Our Mission & Philosophy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 319px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: Mahmoudou%20Sidibe%20Profile%20Photo.jpg (natural: 1000px, display: 343px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: Professional-wellness-marketing-consultation.jpg (natural: 2048px, display: 343px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 343px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13/14 (414px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\About-414px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 414x7595, client: 414x896)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 414x72, client: 414x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 382x72, client: 382x64)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50.w-fit.mx-auto (12px) - "Our Mission & Philosophy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 358px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: Mahmoudou%20Sidibe%20Profile%20Photo.jpg (natural: 1000px, display: 382px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: Professional-wellness-marketing-consultation.jpg (natural: 2048px, display: 382px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 382px)</div></div>
        <div class="breakpoint">
            <h3>iPad Portrait (768px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\About-768px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 768x6025, client: 768x1024)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 768x66, client: 768x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 736x66, client: 736x64)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50.w-fit.mx-auto (12px) - "Our Mission & Philosophy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-charcoal-700.hover:text-strategy-blue.transition-colors.font-medium (34x24px) - "Blog"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 267px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: Professional-wellness-marketing-consultation.jpg (natural: 2048px, display: 720px)</div></div></div>
    <div class="page-section">
        <h2>Contact (/contact)</h2>

        <div class="breakpoint">
            <h3>iPhone SE (320px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Contact-320px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 320x6153, client: 320x568)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: LEGEND.sr-only (scroll: 157x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: LEGEND.sr-only (scroll: 150x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: SPAN (scroll: 178x40, client: 178x20)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: SELECT (scroll: 1x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl.py-16 (scroll: 338x1120, client: 320x1120)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-5.gap-8 (scroll: 322x835, client: 288x835)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.lg:col-span-2 (scroll: 322x325, client: 320x325)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.space-y-2.text-cloud-grey-100 (scroll: 322x76, client: 320x76)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.mb-6.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50 (12px) - "Begin a Strategic Partnership"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.flex.flex-wrap.justify-center.items-center.gap-4.mt-4.text-xs.text-charcoal-400 (12px) - "SSL SecuredHIPAA Compliant24hr Response"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.flex.items-center (12px) - "SSL Secured"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: SPAN (12px) - "SSL Secured"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.flex.items-center (12px) - "HIPAA Compliant"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-strategy-blue.font-medium.hover:underline.break-all.text-sm.lg:text-base (236x40px) - "partnerships@wellnessmarketing"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 264px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 320px)</div><div class="issue high"><strong>IMAGE-OVERFLOW:</strong> Image overflows viewport: wellness-marketing-maestros-logo.png (width: 320px, viewport: 320px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13 mini (375px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Contact-375px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 375x5765, client: 375x812)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 375x67, client: 375x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 343x67, client: 343x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: LEGEND.sr-only (scroll: 157x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: LEGEND.sr-only (scroll: 150x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: SELECT (scroll: 1x24, client: 1x1)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.mb-6.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50 (12px) - "Begin a Strategic Partnership"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.flex.flex-wrap.justify-center.items-center.gap-4.mt-4.text-xs.text-charcoal-400 (12px) - "SSL SecuredHIPAA Compliant24hr Response"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.flex.items-center (12px) - "SSL Secured"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: SPAN (12px) - "SSL Secured"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.flex.items-center (12px) - "HIPAA Compliant"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-strategy-blue.font-medium.hover:underline.break-all.text-sm.lg:text-base (291x40px) - "partnerships@wellnessmarketing"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 319px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 343px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13/14 (414px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Contact-414px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 414x5598, client: 414x896)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 414x72, client: 414x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 382x72, client: 382x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: LEGEND.sr-only (scroll: 157x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: LEGEND.sr-only (scroll: 150x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: SELECT (scroll: 1x24, client: 1x1)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.mb-6.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50 (12px) - "Begin a Strategic Partnership"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.flex.flex-wrap.justify-center.items-center.gap-4.mt-4.text-xs.text-charcoal-400 (12px) - "SSL SecuredHIPAA Compliant24hr Response"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.flex.items-center (12px) - "SSL Secured"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: SPAN (12px) - "SSL Secured"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.flex.items-center (12px) - "HIPAA Compliant"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-strategy-blue.font-medium.hover:underline.break-all.text-sm.lg:text-base (330x20px) - "partnerships@wellnessmarketing"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 358px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 382px)</div></div>
        <div class="breakpoint">
            <h3>iPad Portrait (768px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Contact-768px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 768x4718, client: 768x1024)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 768x66, client: 768x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 736x66, client: 736x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: LEGEND.sr-only (scroll: 157x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: LEGEND.sr-only (scroll: 150x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: SELECT (scroll: 1x24, client: 1x1)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.mb-6.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50 (12px) - "Begin a Strategic Partnership"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.flex.flex-wrap.justify-center.items-center.gap-4.mt-4.text-xs.text-charcoal-400 (12px) - "SSL SecuredHIPAA Compliant24hr Response"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.flex.items-center (12px) - "SSL Secured"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: SPAN (12px) - "SSL Secured"</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.flex.items-center (12px) - "HIPAA Compliant"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-charcoal-700.hover:text-strategy-blue.transition-colors.font-medium (34x24px) - "Blog"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-strategy-blue.font-medium.hover:underline.break-all.text-sm.lg:text-base (684x20px) - "partnerships@wellnessmarketing"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 267px)</div></div></div>
    <div class="page-section">
        <h2>Growth Audit (/growth-audit)</h2>

        <div class="breakpoint">
            <h3>iPhone SE (320px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Growth-Audit-320px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 320x10485, client: 320x568)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 354x930, client: 320x930)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.max-w-4xl.mx-auto.text-center (scroll: 338x930, client: 288x930)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.inline-flex.items-center.justify-center.gap-2.whitespace-nowrap.ring-offset-background.focus-visible:outline-none.focus-visible:ring-4.focus-visible:ring-strategy-blue.focus-visible:ring-offset-2.disabled:pointer-events-none.disabled:opacity-50.[&_svg]:pointer-events-none.[&_svg]:size-4.[&_svg]:shrink-0.touch-manipulation.max-w-full.bg-strategy-blue-600.text-white.hover:bg-strategy-blue-700.shadow-md.hover:shadow-lg.transition-all.duration-200.border-2.border-insight-gold.hover:border-insight-gold-600.h-14.rounded-md.px-8.text-base.font-semibold.min-w-[44px].mb-12 (scroll: 336x52, client: 284x52)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl.py-16 (scroll: 338x1120, client: 320x1120)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-5.gap-8 (scroll: 322x835, client: 288x835)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.lg:col-span-2 (scroll: 322x325, client: 320x325)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.space-y-2.text-cloud-grey-100 (scroll: 322x76, client: 320x76)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center (scroll: 322x20, client: 320x20)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.mb-6.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50 (12px) - "Strategic Foundation Service"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (238x28px) - "For Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (238x28px) - "For Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (238x28px) - "For Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 264px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-process.png (natural: 1536px, display: 288px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 320px)</div><div class="issue high"><strong>IMAGE-OVERFLOW:</strong> Image overflows viewport: wellness-marketing-maestros-logo.png (width: 320px, viewport: 320px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13 mini (375px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Growth-Audit-375px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 375x9471, client: 375x812)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 375x67, client: 375x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 343x67, client: 343x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 382x876, client: 375x876)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.max-w-4xl.mx-auto.text-center (scroll: 366x876, client: 343x876)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.inline-flex.items-center.justify-center.gap-2.whitespace-nowrap.ring-offset-background.focus-visible:outline-none.focus-visible:ring-4.focus-visible:ring-strategy-blue.focus-visible:ring-offset-2.disabled:pointer-events-none.disabled:opacity-50.[&_svg]:pointer-events-none.[&_svg]:size-4.[&_svg]:shrink-0.touch-manipulation.max-w-full.bg-strategy-blue-600.text-white.hover:bg-strategy-blue-700.shadow-md.hover:shadow-lg.transition-all.duration-200.border-2.border-insight-gold.hover:border-insight-gold-600.h-14.rounded-md.px-8.text-base.font-semibold.min-w-[44px].mb-12 (scroll: 364x52, client: 339x52)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.mb-6.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50 (12px) - "Strategic Foundation Service"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-strategy-blue.hover:underline (215x20px) - "Core Implementation Project"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (293x28px) - "For Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (293x28px) - "For Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (293x28px) - "For Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 319px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-process.png (natural: 1536px, display: 343px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 343px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13/14 (414px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Growth-Audit-414px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 414x9039, client: 414x896)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 414x72, client: 414x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 382x72, client: 382x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.max-w-4xl.mx-auto.text-center (scroll: 385x847, client: 382x847)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.inline-flex.items-center.justify-center.gap-2.whitespace-nowrap.ring-offset-background.focus-visible:outline-none.focus-visible:ring-4.focus-visible:ring-strategy-blue.focus-visible:ring-offset-2.disabled:pointer-events-none.disabled:opacity-50.[&_svg]:pointer-events-none.[&_svg]:size-4.[&_svg]:shrink-0.touch-manipulation.max-w-full.bg-strategy-blue-600.text-white.hover:bg-strategy-blue-700.shadow-md.hover:shadow-lg.transition-all.duration-200.border-2.border-insight-gold.hover:border-insight-gold-600.h-14.rounded-md.px-8.text-base.font-semibold.min-w-[44px].mb-12 (scroll: 383x52, client: 378x52)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.mb-6.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50 (12px) - "Strategic Foundation Service"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (332x28px) - "For Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (332x28px) - "For Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (332x28px) - "For Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 358px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-process.png (natural: 1536px, display: 382px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 382px)</div></div>
        <div class="breakpoint">
            <h3>iPad Portrait (768px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Growth-Audit-768px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 768x7296, client: 768x1024)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 768x66, client: 768x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 736x66, client: 736x64)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.mb-6.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50 (12px) - "Strategic Foundation Service"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-charcoal-700.hover:text-strategy-blue.transition-colors.font-medium (34x24px) - "Blog"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-strategy-blue.hover:underline (215x20px) - "Core Implementation Project"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (179x28px) - "For Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (179x28px) - "For Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 267px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-process.png (natural: 1536px, display: 736px)</div></div></div>
    <div class="page-section">
        <h2>Core Implementation (/core-implementation)</h2>

        <div class="breakpoint">
            <h3>iPhone SE (320px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Core-Implementation-320px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 320x10866, client: 320x568)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 325x1007, client: 320x1007)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.max-w-4xl.mx-auto.text-center (scroll: 309x1007, client: 288x1007)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.inline-flex.items-center.justify-center.gap-2.whitespace-nowrap.ring-offset-background.focus-visible:outline-none.focus-visible:ring-4.focus-visible:ring-strategy-blue.focus-visible:ring-offset-2.disabled:pointer-events-none.disabled:opacity-50.[&_svg]:pointer-events-none.[&_svg]:size-4.[&_svg]:shrink-0.touch-manipulation.max-w-full.bg-strategy-blue-600.text-white.hover:bg-strategy-blue-700.shadow-md.hover:shadow-lg.transition-all.duration-200.border-2.border-insight-gold.hover:border-insight-gold-600.h-14.rounded-md.px-8.text-base.font-semibold.min-w-[44px].mb-12 (scroll: 307x52, client: 284x52)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.max-w-4xl.mx-auto.text-center (scroll: 296x853, client: 288x853)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.inline-flex.items-center.justify-center.gap-2.whitespace-nowrap.ring-offset-background.focus-visible:outline-none.focus-visible:ring-4.focus-visible:ring-strategy-blue.focus-visible:ring-offset-2.disabled:pointer-events-none.disabled:opacity-50.[&_svg]:pointer-events-none.[&_svg]:size-4.[&_svg]:shrink-0.touch-manipulation.max-w-full.bg-strategy-blue-600.text-white.hover:bg-strategy-blue-700.shadow-md.hover:shadow-lg.transition-all.duration-200.border-2.border-insight-gold.hover:border-insight-gold-600.h-14.rounded-md.px-8.text-base.font-semibold.min-w-[44px].w-fit (scroll: 294x52, client: 284x52)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl.py-16 (scroll: 338x1120, client: 320x1120)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-5.gap-8 (scroll: 322x835, client: 288x835)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.lg:col-span-2 (scroll: 322x325, client: 320x325)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.mb-6.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50 (12px) - "Strategic Execution Service"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue-600.transition-colors (238x28px) - "For Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue-600.transition-colors (238x28px) - "For Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue-600.transition-colors (238x28px) - "For Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 264px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-implementation-timeline.png (natural: 1536px, display: 288px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 320px)</div><div class="issue high"><strong>IMAGE-OVERFLOW:</strong> Image overflows viewport: wellness-marketing-maestros-logo.png (width: 320px, viewport: 320px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13 mini (375px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Core-Implementation-375px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 375x9921, client: 375x812)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 375x67, client: 375x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 343x67, client: 343x64)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.mb-6.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50 (12px) - "Strategic Execution Service"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue-600.transition-colors (293x28px) - "For Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue-600.transition-colors (293x28px) - "For Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue-600.transition-colors (293x28px) - "For Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 319px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-implementation-timeline.png (natural: 1536px, display: 343px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 343px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13/14 (414px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Core-Implementation-414px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 414x9366, client: 414x896)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 414x72, client: 414x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 382x72, client: 382x64)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.mb-6.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50 (12px) - "Strategic Execution Service"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue-600.transition-colors (332x28px) - "For Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue-600.transition-colors (332x28px) - "For Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue-600.transition-colors (332x28px) - "For Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 358px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-implementation-timeline.png (natural: 1536px, display: 382px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 382px)</div></div>
        <div class="breakpoint">
            <h3>iPad Portrait (768px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Core-Implementation-768px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 768x7500, client: 768x1024)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 768x66, client: 768x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 736x66, client: 736x64)</div><div class="issue medium"><strong>SMALL-TEXT:</strong> Text too small for mobile: DIV.inline-flex.items-center.rounded-full.border.px-2.5.py-0.5.text-xs.font-semibold.transition-colors.focus:outline-none.focus:ring-2.focus:ring-ring.focus:ring-offset-2.mb-6.text-strategy-blue.border-strategy-blue-200.bg-strategy-blue-50 (12px) - "Strategic Execution Service"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-charcoal-700.hover:text-strategy-blue.transition-colors.font-medium (34x24px) - "Blog"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-strategy-blue-600.hover:underline (361x20px) - "Growth Audit & Strategic Roadm"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue-600.transition-colors (179x28px) - "For Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue-600.transition-colors (179x28px) - "For Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 267px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-implementation-timeline.png (natural: 1536px, display: 736px)</div></div></div>
    <div class="page-section">
        <h2>Wellness Clinics (/wellness-clinics)</h2>

        <div class="breakpoint">
            <h3>iPhone SE (320px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Wellness-Clinics-320px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 320x8129, client: 320x568)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.max-w-4xl.mx-auto.text-center (scroll: 294x724, client: 288x724)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.inline-flex.items-center.justify-center.gap-2.whitespace-nowrap.ring-offset-background.focus-visible:outline-none.focus-visible:ring-4.focus-visible:ring-strategy-blue.focus-visible:ring-offset-2.disabled:pointer-events-none.disabled:opacity-50.[&_svg]:pointer-events-none.[&_svg]:size-4.[&_svg]:shrink-0.touch-manipulation.max-w-full.bg-strategy-blue-600.text-white.hover:bg-strategy-blue-700.shadow-md.hover:shadow-lg.transition-all.duration-200.border-2.border-insight-gold.hover:border-insight-gold-600.h-14.rounded-md.px-8.text-base.font-semibold.min-w-[44px] (scroll: 292x52, client: 284x52)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl.py-16 (scroll: 338x1120, client: 320x1120)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-5.gap-8 (scroll: 322x835, client: 288x835)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.lg:col-span-2 (scroll: 322x325, client: 320x325)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.space-y-2.text-cloud-grey-100 (scroll: 322x76, client: 320x76)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center (scroll: 322x20, client: 320x20)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 264px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 320px)</div><div class="issue high"><strong>IMAGE-OVERFLOW:</strong> Image overflows viewport: wellness-marketing-maestros-logo.png (width: 320px, viewport: 320px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13 mini (375px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Wellness-Clinics-375px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 375x7249, client: 375x812)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 375x67, client: 375x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 343x67, client: 343x64)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 319px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 343px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13/14 (414px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Wellness-Clinics-414px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 414x6979, client: 414x896)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 414x72, client: 414x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 382x72, client: 382x64)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 358px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 382px)</div></div>
        <div class="breakpoint">
            <h3>iPad Portrait (768px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Wellness-Clinics-768px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 768x5433, client: 768x1024)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 768x66, client: 768x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 736x66, client: 736x64)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-charcoal-700.hover:text-strategy-blue.transition-colors.font-medium (34x24px) - "Blog"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (390x24px) - "Clinic Growth Audit & Strategi"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 267px)</div></div></div>
    <div class="page-section">
        <h2>Medical Spas (/medical-spas)</h2>

        <div class="breakpoint">
            <h3>iPhone SE (320px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Medical-Spas-320px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 320x8315, client: 320x568)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 331x760, client: 320x760)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.max-w-4xl.mx-auto.text-center (scroll: 315x760, client: 288x760)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.inline-flex.items-center.justify-center.gap-2.whitespace-nowrap.ring-offset-background.focus-visible:outline-none.focus-visible:ring-4.focus-visible:ring-strategy-blue.focus-visible:ring-offset-2.disabled:pointer-events-none.disabled:opacity-50.[&_svg]:pointer-events-none.[&_svg]:size-4.[&_svg]:shrink-0.touch-manipulation.max-w-full.bg-strategy-blue-600.text-white.hover:bg-strategy-blue-700.shadow-md.hover:shadow-lg.transition-all.duration-200.border-2.border-insight-gold.hover:border-insight-gold-600.h-14.rounded-md.px-8.text-base.font-semibold.min-w-[44px] (scroll: 313x52, client: 284x52)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl.py-16 (scroll: 338x1120, client: 320x1120)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-5.gap-8 (scroll: 322x835, client: 288x835)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.lg:col-span-2 (scroll: 322x325, client: 320x325)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.space-y-2.text-cloud-grey-100 (scroll: 322x76, client: 320x76)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center (scroll: 322x20, client: 320x20)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 264px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 320px)</div><div class="issue high"><strong>IMAGE-OVERFLOW:</strong> Image overflows viewport: wellness-marketing-maestros-logo.png (width: 320px, viewport: 320px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13 mini (375px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Medical-Spas-375px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 375x7387, client: 375x812)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 375x67, client: 375x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 343x67, client: 343x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.inline-flex.items-center.justify-center.gap-2.whitespace-nowrap.ring-offset-background.focus-visible:outline-none.focus-visible:ring-4.focus-visible:ring-strategy-blue.focus-visible:ring-offset-2.disabled:pointer-events-none.disabled:opacity-50.[&_svg]:pointer-events-none.[&_svg]:size-4.[&_svg]:shrink-0.touch-manipulation.max-w-full.bg-strategy-blue-600.text-white.hover:bg-strategy-blue-700.shadow-md.hover:shadow-lg.transition-all.duration-200.border-2.border-insight-gold.hover:border-insight-gold-600.h-14.rounded-md.px-8.text-base.font-semibold.min-w-[44px] (scroll: 340x52, client: 339x52)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 319px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 343px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13/14 (414px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Medical-Spas-414px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 414x6985, client: 414x896)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 414x72, client: 414x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 382x72, client: 382x64)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 358px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 382px)</div></div>
        <div class="breakpoint">
            <h3>iPad Portrait (768px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Medical-Spas-768px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 768x5433, client: 768x1024)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 768x66, client: 768x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 736x66, client: 736x64)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-charcoal-700.hover:text-strategy-blue.transition-colors.font-medium (34x24px) - "Blog"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-strategy-blue.transition-colors (376x24px) - "MedSpa Brand Equity Audit & Ro"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 267px)</div></div></div>
    <div class="page-section">
        <h2>Holistic Functional Medicine (/holistic-functional-medicine)</h2>

        <div class="breakpoint">
            <h3>iPhone SE (320px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Holistic-Functional-Medicine-320px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 320x8526, client: 320x568)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl.py-16 (scroll: 338x1120, client: 320x1120)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-5.gap-8 (scroll: 322x835, client: 288x835)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.lg:col-span-2 (scroll: 322x325, client: 320x325)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.space-y-2.text-cloud-grey-100 (scroll: 322x76, client: 320x76)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center (scroll: 322x20, client: 320x20)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 264px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 320px)</div><div class="issue high"><strong>IMAGE-OVERFLOW:</strong> Image overflows viewport: wellness-marketing-maestros-logo.png (width: 320px, viewport: 320px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13 mini (375px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Holistic-Functional-Medicine-375px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 375x7489, client: 375x812)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 375x67, client: 375x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 343x67, client: 343x64)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 319px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 343px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13/14 (414px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Holistic-Functional-Medicine-414px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 414x7063, client: 414x896)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 414x72, client: 414x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 382x72, client: 382x64)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 358px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 382px)</div></div>
        <div class="breakpoint">
            <h3>iPad Portrait (768px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Holistic-Functional-Medicine-768px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 768x5436, client: 768x1024)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 768x66, client: 768x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 736x66, client: 736x64)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-charcoal-700.hover:text-strategy-blue.transition-colors.font-medium (34x24px) - "Blog"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.hover:text-charcoal-700.transition-colors (435x24px) - "Practice Authority Audit & Str"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 267px)</div></div></div>
    <div class="page-section">
        <h2>Multi-practitioner Centers (/multi-practitioner-centers)</h2>

        <div class="breakpoint">
            <h3>iPhone SE (320px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Multi-practitioner-Centers-320px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 320x8970, client: 320x568)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 334x792, client: 320x792)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.max-w-4xl.mx-auto.text-center (scroll: 318x792, client: 288x792)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.inline-flex.items-center.justify-center.gap-2.whitespace-nowrap.ring-offset-background.focus-visible:outline-none.focus-visible:ring-4.focus-visible:ring-strategy-blue.focus-visible:ring-offset-2.disabled:pointer-events-none.disabled:opacity-50.[&_svg]:pointer-events-none.[&_svg]:size-4.[&_svg]:shrink-0.touch-manipulation.max-w-full.bg-strategy-blue-600.text-white.hover:bg-strategy-blue-700.shadow-md.hover:shadow-lg.transition-all.duration-200.border-2.border-insight-gold.hover:border-insight-gold-600.h-14.rounded-md.px-8.text-base.font-semibold.min-w-[44px] (scroll: 316x52, client: 284x52)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl.py-16 (scroll: 338x1120, client: 320x1120)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.grid.lg:grid-cols-5.gap-8 (scroll: 322x835, client: 288x835)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.lg:col-span-2 (scroll: 322x325, client: 320x325)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.space-y-2.text-cloud-grey-100 (scroll: 322x76, client: 320x76)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center (scroll: 322x20, client: 320x20)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 264px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 320px)</div><div class="issue high"><strong>IMAGE-OVERFLOW:</strong> Image overflows viewport: wellness-marketing-maestros-logo.png (width: 320px, viewport: 320px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13 mini (375px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Multi-practitioner-Centers-375px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 375x7833, client: 375x812)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 375x67, client: 375x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 343x67, client: 343x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.max-w-4xl.mx-auto.text-center (scroll: 346x659, client: 343x659)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.inline-flex.items-center.justify-center.gap-2.whitespace-nowrap.ring-offset-background.focus-visible:outline-none.focus-visible:ring-4.focus-visible:ring-strategy-blue.focus-visible:ring-offset-2.disabled:pointer-events-none.disabled:opacity-50.[&_svg]:pointer-events-none.[&_svg]:size-4.[&_svg]:shrink-0.touch-manipulation.max-w-full.bg-strategy-blue-600.text-white.hover:bg-strategy-blue-700.shadow-md.hover:shadow-lg.transition-all.duration-200.border-2.border-insight-gold.hover:border-insight-gold-600.h-14.rounded-md.px-8.text-base.font-semibold.min-w-[44px] (scroll: 344x52, client: 339x52)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 319px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 343px)</div></div>
        <div class="breakpoint">
            <h3>iPhone 12/13/14 (414px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Multi-practitioner-Centers-414px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 414x7406, client: 414x896)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 414x72, client: 414x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 382x72, client: 382x64)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: BUTTON.md:hidden.flex.items-center.justify-center.w-10.h-10.rounded-lg.hover:bg-cloud-grey-100.transition-colors (24x56px) - ""</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 358px)</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 382px)</div></div>
        <div class="breakpoint">
            <h3>iPad Portrait (768px)</h3>
<img src="C:\AI Coding Projects\Clients\wellness-marketing-maestros\mobile-audit-screenshots\Multi-practitioner-Centers-768px.png" alt="Screenshot" class="screenshot"><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: HTML (scroll: 768x5663, client: 768x1024)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (scroll: 155x24, client: 1x1)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.container.mx-auto.px-4.max-w-7xl (scroll: 768x66, client: 768x64)</div><div class="issue medium"><strong>ELEMENT-OVERFLOW:</strong> Element overflows container: DIV.flex.items-center.justify-between.h-16 (scroll: 736x66, client: 736x64)</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.sr-only.focus:not-sr-only.focus:absolute.focus:top-4.focus:left-4.bg-strategy-blue.text-white.px-4.py-2.rounded-md.z-50 (1x1px) - "Skip to main content"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-charcoal-700.hover:text-strategy-blue.transition-colors.font-medium (34x24px) - "Blog"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (45x20px) - "Home"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (70x20px) - "About Us"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (59x20px) - "Contact"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (99x20px) - "Growth Audit"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (167x20px) - "Strategic Consultation"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (123x20px) - "Wellness Clinics"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (102x20px) - "Medical Spas"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (130x20px) - "Holistic Medicine"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-100.hover:text-strategy-blue.transition-colors (195x20px) - "Multi-Practitioner Centers"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (93x20px) - "Privacy Policy"</div><div class="issue high"><strong>SMALL-TOUCH-TARGET:</strong> Touch target too small: A.text-cloud-grey-400.hover:text-strategy-blue.text-sm.transition-colors (113x20px) - "Terms of Service"</div><div class="issue medium"><strong>OVERSIZED-IMAGE:</strong> Image much larger than display size: wellness-marketing-maestros-logo.png (natural: 901px, display: 267px)</div></div></div>
</body>
</html>